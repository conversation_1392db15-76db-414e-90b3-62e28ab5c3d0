import { useState } from 'react'

const CabinetManagement = () => {
  const [cabinetsData, setCabinetsData] = useState([
    {
      id: 1,
      title: 'خزانة ملابس عصرية',
      description: 'خزانة ملابس بتصميم عصري وعملي',
      images: ['/images/cabinet1.jpg', '/images/cabinet2.jpg'],
      category: 'خزائن ملابس',
      features: ['مساحة تخزين كبيرة', 'تصميم أنيق', 'خامات متينة']
    },
    {
      id: 2,
      title: 'خزانة مطبخ',
      description: 'خزانة مطبخ عملية وجميلة',
      images: ['/images/cabinet3.jpg'],
      category: 'خزائن مطبخ',
      features: ['مقاومة للرطوبة', 'سهولة التنظيف', 'تصميم عملي']
    }
  ])
  
  const [isAdding, setIsAdding] = useState(false)
  const [editingId, setEditingId] = useState(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    images: [],
    category: '',
    features: []
  })

  const handleAdd = () => {
    const newCabinet = {
      id: Date.now(),
      ...formData
    }
    setCabinetsData([...cabinetsData, newCabinet])
    setFormData({ title: '', description: '', images: [], category: '', features: [] })
    setIsAdding(false)
    alert('تم إضافة الخزانة بنجاح!')
  }

  const handleEdit = (cabinet) => {
    setFormData(cabinet)
    setEditingId(cabinet.id)
  }

  const handleUpdate = () => {
    setCabinetsData(cabinetsData.map(cabinet => 
      cabinet.id === editingId ? formData : cabinet
    ))
    setEditingId(null)
    setFormData({ title: '', description: '', images: [], category: '', features: [] })
    alert('تم تحديث الخزانة بنجاح!')
  }

  const handleDelete = (id) => {
    if (confirm('هل أنت متأكد من حذف هذه الخزانة؟')) {
      setCabinetsData(cabinetsData.filter(cabinet => cabinet.id !== id))
      alert('تم حذف الخزانة بنجاح!')
    }
  }

  return (
    <div className="admin-dashboard space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">إدارة الخزائن</h1>
          <p className="text-gray-600 mt-2">إضافة وتعديل وحذف الخزائن</p>
        </div>
        <button
          onClick={() => setIsAdding(true)}
          className="btn-primary bg-purple-600 hover:bg-purple-700"
        >
          <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          إضافة خزانة جديدة
        </button>
      </div>

      {/* Add/Edit Form */}
      {(isAdding || editingId) && (
        <div className="admin-card p-8">
          <h2 className="text-2xl font-bold mb-6 text-gray-800">
            {isAdding ? 'إضافة خزانة جديدة' : 'تعديل الخزانة'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                عنوان الخزانة
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className="form-input"
                placeholder="أدخل عنوان الخزانة"
              />
            </div>
            
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                الفئة
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({...formData, category: e.target.value})}
                className="form-input"
              >
                <option value="">اختر الفئة</option>
                <option value="خزائن ملابس">خزائن ملابس</option>
                <option value="خزائن مطبخ">خزائن مطبخ</option>
                <option value="خزائن حمام">خزائن حمام</option>
                <option value="خزائن مكتب">خزائن مكتب</option>
              </select>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                الوصف
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="form-textarea"
                rows="4"
                placeholder="أدخل وصف الخزانة"
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-4 mt-8">
            <button
              onClick={() => {
                setIsAdding(false)
                setEditingId(null)
                setFormData({ title: '', description: '', images: [], category: '', features: [] })
              }}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              onClick={isAdding ? handleAdd : handleUpdate}
              className="btn-primary bg-purple-600 hover:bg-purple-700"
            >
              {isAdding ? 'إضافة' : 'تحديث'}
            </button>
          </div>
        </div>
      )}

      {/* Cabinets List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {cabinetsData.map((cabinet) => (
          <div key={cabinet.id} className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="h-48 bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500">صورة الخزانة</span>
            </div>
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">{cabinet.title}</h3>
              <p className="text-gray-600 mb-3">{cabinet.description}</p>
              <div className="flex items-center justify-between">
                <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                  {cabinet.category}
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEdit(cabinet)}
                    className="text-purple-600 hover:text-purple-800 p-2"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => handleDelete(cabinet.id)}
                    className="text-red-600 hover:text-red-800 p-2"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {cabinetsData.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🗄️</div>
          <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد خزائن</h3>
          <p className="text-gray-500">ابدأ بإضافة أول خزانة</p>
        </div>
      )}
    </div>
  )
}

export default CabinetManagement
