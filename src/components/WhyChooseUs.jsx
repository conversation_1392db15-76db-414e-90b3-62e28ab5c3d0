import { useEffect, useRef, useState } from 'react';

const WhyChooseUs = () => {
  const sectionRef = useRef(null);
  const [visibleCards, setVisibleCards] = useState([]);

  // البيانات الافتراضية (نفس البيانات الموجودة في DataContext)
  const defaultWhyChooseUsData = {
    title: 'لماذا تختار خبرة المطابخ؟',
    subtitle: 'نحن نقدم أفضل الحلول لتصميم وتنفيذ المطابخ والخزائن بجودة عالية وأسعار منافسة',
    features: [
      {
        id: 1,
        icon: 'ri-award-line',
        title: 'خبرة أكثر من 15 عاماً',
        description: 'فريق من المصممين والحرفيين ذوي الخبرة الطويلة في مجال تصميم وتنفيذ المطابخ والخزائن بأعلى معايير الجودة.',
        gradient: 'from-blue-500 to-cyan-500',
        bgGradient: 'from-blue-50 to-cyan-50',
        number: '15+',
        subtitle: 'سنة خبرة'
      },
      {
        id: 2,
        icon: 'ri-palette-line',
        title: 'تصاميم فريدة ومبتكرة',
        description: 'نقدم تصاميم عصرية تناسب ذوقك واحتياجاتك، مع الاهتمام بأدق التفاصيل لإنشاء مساحات تجمع بين الجمال والوظيفة.',
        gradient: 'from-purple-500 to-pink-500',
        bgGradient: 'from-purple-50 to-pink-50',
        number: '500+',
        subtitle: 'تصميم فريد'
      },
      {
        id: 3,
        icon: 'ri-tools-line',
        title: 'مواد عالية الجودة',
        description: 'نستخدم أفضل الخامات المستوردة والمحلية لضمان متانة ودوام منتجاتنا، مع ضمان يصل إلى 10 سنوات على جميع أعمالنا.',
        gradient: 'from-green-500 to-emerald-500',
        bgGradient: 'from-green-50 to-emerald-50',
        number: '10',
        subtitle: 'سنوات ضمان'
      }
    ]
  };

  const [whyChooseUsData, setWhyChooseUsData] = useState(defaultWhyChooseUsData);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const fadeElements = entry.target.querySelectorAll('.fade-in');
            fadeElements.forEach((element, index) => {
              setTimeout(() => {
                element.classList.add('visible');
                setVisibleCards(prev => [...prev, index]);
              }, index * 200);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // قراءة البيانات من localStorage عند تحميل المكون
  useEffect(() => {
    const loadWhyChooseUsData = () => {
      const savedWhyChooseUsData = localStorage.getItem('admin_why_choose_us_data');
      if (savedWhyChooseUsData) {
        try {
          const parsedData = JSON.parse(savedWhyChooseUsData);
          setWhyChooseUsData(parsedData);
        } catch (error) {
          console.error('Error parsing why choose us data:', error);
          setWhyChooseUsData(defaultWhyChooseUsData);
        }
      }
    };

    // تحميل البيانات عند بداية المكون
    loadWhyChooseUsData();

    // إضافة listener للتحديثات الفورية
    const handleStorageChange = (e) => {
      if (e.key === 'admin_why_choose_us_data') {
        loadWhyChooseUsData();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // تنظيف الـ listener عند إزالة المكون
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <section id="why-us" className="relative py-24 bg-gradient-to-br from-gray-50 via-white to-blue-50 overflow-hidden" ref={sectionRef}>
      {/* Background Decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-200/30 to-orange-200/30 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl mb-6 shadow-lg">
            <i className="ri-star-line text-2xl text-white"></i>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
            {whyChooseUsData.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {whyChooseUsData.subtitle}
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-8 rounded-full"></div>
        </div>

        {/* Enhanced Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {whyChooseUsData.features?.map((feature, index) => (
            <div
              key={index}
              className={`fade-in group relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover:bg-white transition-all duration-500 hover:scale-105 hover:shadow-2xl border border-white/50 ${
                visibleCards.includes(index) ? 'animate-bounce-in' : ''
              }`}
              style={{ animationDelay: `${index * 200}ms` }}
            >
              {/* Card Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgGradient} rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>

              {/* Content */}
              <div className="relative z-10">
                {/* Icon Container */}
                <div className="relative mb-8">
                  <div className={`w-20 h-20 mx-auto bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${feature.icon} text-3xl text-white`}></i>
                  </div>
                  {/* Floating Number */}
                  <div className="absolute -top-2 -right-2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-4 border-gray-50">
                    <span className={`text-sm font-bold bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent`}>
                      {feature.number}
                    </span>
                  </div>
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-gray-900 transition-colors duration-300">
                  {feature.title}
                </h3>

                {/* Subtitle */}
                <p className={`text-sm font-medium bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent mb-4`}>
                  {feature.subtitle}
                </p>

                {/* Description */}
                <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                  {feature.description}
                </p>

                {/* Decorative Line */}
                <div className={`w-0 h-1 bg-gradient-to-r ${feature.gradient} group-hover:w-full transition-all duration-500 mt-6 rounded-full`}></div>
              </div>

              {/* Hover Effect Border */}
              <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`} style={{ padding: '2px' }}>
                <div className="w-full h-full bg-white rounded-3xl"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA Section */}
        <div className="text-center mt-20">
          <div className="inline-flex items-center space-x-4 rtl:space-x-reverse bg-white/80 backdrop-blur-sm rounded-2xl px-8 py-4 shadow-lg border border-white/50">
            <div className="flex -space-x-2 rtl:space-x-reverse">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="w-10 h-10 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center border-2 border-white">
                  <i className="ri-star-fill text-white text-sm"></i>
                </div>
              ))}
            </div>
            <div className="text-right rtl:text-left">
              <p className="text-lg font-bold text-gray-800">تقييم 5 نجوم</p>
              <p className="text-sm text-gray-600">من أكثر من 1000 عميل راضٍ</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
