import { useState, useEffect } from 'react'

const HeroSection = () => {
  // البيانات الافتراضية (نفس البيانات الموجودة في DataContext)
  const defaultHeroData = {
    title: 'خبرة المطابخ - مطابخ تفوق التوقعات، وخزائن بتصاميم لا تُنسى',
    subtitle: 'نحول مساحات منزلك إلى تحف فنية تجمع بين الجمال والوظيفة بلمسة من الإبداع والحرفية العالية في خبرة المطابخ',
    backgroundImage: 'https://readdy.ai/api/search-image?query=luxurious%20modern%20kitchen%20with%20elegant%20design%2C%20marble%20countertops%2C%20wooden%20cabinets%2C%20high-end%20appliances%2C%20soft%20lighting%2C%20spacious%20layout%2C%20minimalist%20style%2C%20professional%20photography%2C%20high%20resolution%2C%20advertisement%20quality&width=1920&height=1080&seq=1&orientation=landscape',
    primaryButtonText: 'شاهد تصاميمنا',
    secondaryButtonText: 'تواصل معنا'
  };

  const [heroData, setHeroData] = useState(defaultHeroData);

  // قراءة البيانات من localStorage عند تحميل المكون
  useEffect(() => {
    const loadHeroData = () => {
      const savedHeroData = localStorage.getItem('admin_hero_data');
      if (savedHeroData) {
        try {
          const parsedData = JSON.parse(savedHeroData);
          setHeroData(parsedData);
        } catch (error) {
          console.error('Error parsing hero data:', error);
          setHeroData(defaultHeroData);
        }
      }
    };

    // تحميل البيانات عند بداية المكون
    loadHeroData();

    // إضافة listener للتحديثات الفورية
    const handleStorageChange = (e) => {
      if (e.key === 'admin_hero_data') {
        loadHeroData();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // تنظيف الـ listener عند إزالة المكون
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const scrollToKitchens = () => {
    document.getElementById('kitchens')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section
      id="home"
      className="min-h-screen flex items-center relative"
      style={{
        backgroundImage: `linear-gradient(to left, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7)), url('${heroData.backgroundImage}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="container mx-auto px-6 py-20 w-full relative z-10">
        <div className="max-w-4xl">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight animate-fade-in">
            {heroData.title}
          </h1>
          <p className="text-xl text-gray-100 mb-8 leading-relaxed animate-fade-in-delay">
            {heroData.subtitle}
          </p>
          <div className="flex flex-wrap gap-4 animate-fade-in-delay-2">
            <button
              onClick={scrollToKitchens}
              className="bg-primary hover:bg-blue-600 text-white px-8 py-4 rounded-button text-lg font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg whitespace-nowrap"
            >
              {heroData.primaryButtonText}
            </button>
            <button
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white px-8 py-4 rounded-button text-lg font-medium transition-all duration-300 transform hover:scale-105 whitespace-nowrap"
            >
              {heroData.secondaryButtonText}
            </button>
          </div>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <i className="ri-arrow-down-line text-2xl"></i>
      </div>
    </section>
  );
};

export default HeroSection;
